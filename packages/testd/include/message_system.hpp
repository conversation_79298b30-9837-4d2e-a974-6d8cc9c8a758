#pragma once

#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <set>
#include <unordered_set>
#include <json-c/json.h>
#include "context.hpp"

namespace testd {

/**
 * @brief 消息优先级枚举
 */
enum class MessagePriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * @brief 消息来源类型
 */
enum class MessageSource {
    CLIENT = 0,      // 来自被控端
    INTERNAL = 1,    // 内部消息
    TIMER = 2,       // 定时器消息
    EXTERNAL = 3     // 外部系统消息
};

/**
 * @brief 增强的消息类型
 */
struct EnhancedMessage {
    // 基本信息
    std::string id;
    int idInt;
    std::string type;
    std::string subType;
    
    // 元数据
    MessagePriority priority;
    MessageSource source;
    std::string sourceId;  // 来源标识（如客户端ID、进程名等）
    uint64_t timestamp;    // 时间戳

    // 连接信息
    std::weak_ptr<ClientConnection> clientConnection;  // 客户端连接
    std::string clientUUID;       // 客户端UUID
    
    // 内容
    int result;
    std::string error;
    struct json_object* data;
    
    // 路由信息
    std::vector<std::string> tags;  // 消息标签，用于灵活过滤
    
    EnhancedMessage();
    ~EnhancedMessage();
    EnhancedMessage(const EnhancedMessage& other);
    EnhancedMessage& operator=(const EnhancedMessage& other);
    

    
    // 设置数据
    void setData(struct json_object* d);
    struct json_object* getData() const { return data; }
    
    // 添加标签
    void addTag(const std::string& tag);
    bool hasTag(const std::string& tag) const;
};

/**
 * @brief 消息过滤器
 */
class MessageFilter {
public:
    MessageFilter() = default;
    virtual ~MessageFilter() = default;
    
    // 设置过滤条件
    MessageFilter& byType(const std::string& type);
    MessageFilter& bySubType(const std::string& subType);
    MessageFilter& bySource(MessageSource source);
    MessageFilter& bySourceId(const std::string& sourceId);
    MessageFilter& byPriority(MessagePriority minPriority);
    MessageFilter& byTag(const std::string& tag);
    MessageFilter& byClientConnection(std::shared_ptr<ClientConnection> client);
    MessageFilter& byClientUUID(const std::string& clientUUID);
    MessageFilter& byCustom(std::function<bool(const EnhancedMessage&)> predicate);
    
    // 检查消息是否匹配过滤条件
    bool matches(const EnhancedMessage& msg) const;
    
private:
    std::unordered_set<std::string> types_;
    std::unordered_set<std::string> subTypes_;
    std::unordered_set<MessageSource> sources_;
    std::unordered_set<std::string> sourceIds_;
    MessagePriority minPriority_ = MessagePriority::LOW;
    std::unordered_set<std::string> tags_;
    std::vector<std::weak_ptr<ClientConnection>> clientConnections_;
    std::unordered_set<std::string> clientUUIDs_;
    std::vector<std::function<bool(const EnhancedMessage&)>> customPredicates_;
};

/**
 * @brief 消息处理器接口
 */
class MessageHandler {
public:
    virtual ~MessageHandler() = default;
    
    /**
     * @brief 处理消息
     * @param msg 要处理的消息
     * @return true表示消息已被处理，false表示继续传递给其他处理器
     */
    virtual bool handleMessage(const EnhancedMessage& msg) = 0;
    
    /**
     * @brief 获取处理器名称
     */
    virtual std::string getName() const = 0;
    
    /**
     * @brief 获取处理器优先级（数值越大优先级越高）
     */
    virtual int getPriority() const { return 0; }

    bool operator<(const MessageHandler& other) const {
        return getPriority() > other.getPriority();
    }
};

/**
 * @brief 消息订阅信息
 */
struct MessageSubscription {
    std::string id;                           // 订阅ID
    MessageFilter filter;                     // 过滤条件
    std::shared_ptr<MessageHandler> handler;  // 处理器
    int priority;                            // 订阅优先级
    bool consumeMessage;                     // 是否消费消息（消费后不再传递给其他订阅者）

    MessageSubscription(const std::string& id,
                       const MessageFilter& filter,
                       std::shared_ptr<MessageHandler> handler,
                       int priority = 0,
                       bool consumeMessage = false);

    // 显式定义拷贝和移动语义
    MessageSubscription(const MessageSubscription& other) = default;
    MessageSubscription& operator=(const MessageSubscription& other) = default;
    MessageSubscription(MessageSubscription&& other) noexcept = default;
    MessageSubscription& operator=(MessageSubscription&& other) noexcept = default;

    bool operator<(const MessageSubscription& other) const {
        return priority > other.priority;
    }
};

/**
 * @brief 消息路由器
 */
class MessageRouter {
public:
    MessageRouter() = default;
    ~MessageRouter() = default;
    
    // 禁止拷贝和赋值
    MessageRouter(const MessageRouter&) = delete;
    MessageRouter& operator=(const MessageRouter&) = delete;
    
    /**
     * @brief 订阅消息
     * @param subscription 订阅信息
     * @return 订阅ID
     */
    std::string subscribe(const MessageSubscription& subscription);
    
    /**
     * @brief 取消订阅
     * @param subscriptionId 订阅ID
     */
    void unsubscribe(const std::string& subscriptionId);
    
    /**
     * @brief 发布消息
     * @param msg 要发布的消息
     */
    void publish(const EnhancedMessage& msg);
    
    /**
     * @brief 获取待处理消息数量
     */
    size_t getPendingMessageCount() const;
    
    /**
     * @brief 处理所有待处理消息
     */
    void processMessages();
    
    /**
     * @brief 清空所有订阅
     */
    void clearSubscriptions();
    
private:
    std::set<MessageSubscription> subscriptions_;
    std::vector<EnhancedMessage> pendingMessages_;
    mutable std::mutex mutex_;
};

/**
 * @brief 消息系统管理器（单例）
 */
class MessageSystem {
public:
    static MessageSystem& getInstance();
    
    // 禁止拷贝和赋值
    MessageSystem(const MessageSystem&) = delete;
    MessageSystem& operator=(const MessageSystem&) = delete;
    
    /**
     * @brief 获取消息路由器
     */
    MessageRouter& getRouter() { return *router_; }
    
    /**
     * @brief 发布消息（便捷方法）
     */
    void publish(const EnhancedMessage& msg);
    
    /**
     * @brief 订阅消息（便捷方法）
     */
    std::string subscribe(const MessageFilter& filter, 
                         std::shared_ptr<MessageHandler> handler,
                         int priority = 0,
                         bool consumeMessage = false);
    
    /**
     * @brief 处理消息
     */
    void processMessages();

    void removeRouter() {
        if (router_) {
            router_->clearSubscriptions();
            delete router_;
            router_ = nullptr;
        }
    }

private:
    MessageSystem() : router_(new MessageRouter()) {};
    ~MessageSystem() { if (router_) delete router_; }
    MessageRouter *router_;
};

} // namespace testd
